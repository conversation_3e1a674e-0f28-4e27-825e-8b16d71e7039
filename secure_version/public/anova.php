<?php
/**
 * Czech ANOVA (Analysis of Variance) test
 * Secure version with input validation and XSS protection
 */

define('STATS_APP', true);

// Include required files
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/security.php';
require_once __DIR__ . '/../includes/functions.php';

// Initialize security and set Czech locale
Security::init();
setAppLocale('cs');

// Get and validate input parameters
$a = Security::getParam('a', 'int', 0);
$in = Security::getParam('in', 'int', null);
$n = Security::getParam('n', 'array', []);
$x = Security::getParam('x', 'array', []);
$csrf_token = Security::getParam('csrf_token', 'string', '');

// Validate CSRF token for form submissions
if ($_SERVER['REQUEST_METHOD'] === 'GET' && $a > 0) {
    if (!Security::validateCsrfToken($csrf_token)) {
        Security::logSecurityEvent("CSRF token validation failed for anova.php");
        $error_message = getMessage('csrf_error', 'cs');
        $a = 0; // Reset to initial state
    }
}

// Error handling
$error_message = '';
$calculation_results = null;

// Statistical functions
function invf($sv, $sw) {
    $fis = fopen(__DIR__ . "/../data/fis3.txt", "r");
    if (!$fis) {
        throw new Exception("Cannot open statistical data file");
    }
    $stav = ($sw - 1) * 151 + ($sv - 1) * 5;
    fseek($fis, $stav);
    $inv = fread($fis, 4);
    fclose($fis);
    return trim($inv);
}

try {
    switch ($a) {
        case 1:
            // Validate number of groups
            if ($in === null || $in < 3 || $in > 10) {
                $error_message = "Počet tříd musí být celé číslo od 3 do 10";
                $a = 0;
            }
            break;

        case 2:
            // Validate group sizes
            if ($in === null || $in < 3 || $in > 10) {
                $error_message = "Počet tříd musí být celé číslo od 3 do 10";
                $a = 1;
            } else {
                $valid_sizes = true;
                for ($i = 0; $i < $in; $i++) {
                    if (!isset($n[$i]) || $n[$i] < 2 || $n[$i] > 10) {
                        $valid_sizes = false;
                        break;
                    }
                }
                if (!$valid_sizes) {
                    $error_message = "Všechny rozsahy tříd musí být čísla od 2 do 10";
                    $a = 1;
                }
            }
            break;

        case 3:
            // Perform ANOVA calculation
            if ($in === null || $in < 3 || $in > 10) {
                $error_message = "Počet tříd musí být celé číslo od 3 do 10";
                $a = 0;
            } else {
                try {
                    // Validate all data inputs
                    $total_n = array_sum($n);
                    if (count($x) !== $total_n) {
                        $error_message = "Počet datových bodů musí odpovídat součtu rozsahů tříd";
                        $a = 2;
                    } else {
                        // Perform ANOVA calculation
                        $calculation_results = performANOVA($x, $n, $in);
                    }
                } catch (Exception $e) {
                    $error_message = getMessage('calculation_error', 'cs');
                    Security::logSecurityEvent("Calculation error in anova.php: " . $e->getMessage());
                    $a = 2;
                }
            }
            break;
    }
} catch (Exception $e) {
    $error_message = getMessage('calculation_error', 'cs');
    Security::logSecurityEvent("Error in anova.php: " . $e->getMessage());
    $a = 0;
}

function performANOVA($data, $group_sizes, $num_groups) {
    $groups = [];
    $index = 0;

    // Split data into groups
    for ($i = 0; $i < $num_groups; $i++) {
        $groups[$i] = array_slice($data, $index, $group_sizes[$i]);
        $index += $group_sizes[$i];
    }

    // Calculate group means and overall mean
    $group_means = [];
    $total_sum = 0;
    $total_n = 0;

    for ($i = 0; $i < $num_groups; $i++) {
        $group_means[$i] = StatFunctions::mean($groups[$i]);
        $total_sum += array_sum($groups[$i]);
        $total_n += count($groups[$i]);
    }

    $overall_mean = $total_sum / $total_n;

    // Calculate sum of squares
    $ss_between = 0;
    $ss_within = 0;

    for ($i = 0; $i < $num_groups; $i++) {
        $ss_between += count($groups[$i]) * pow($group_means[$i] - $overall_mean, 2);
        foreach ($groups[$i] as $value) {
            $ss_within += pow($value - $group_means[$i], 2);
        }
    }

    $df_between = $num_groups - 1;
    $df_within = $total_n - $num_groups;
    $df_total = $total_n - 1;

    $ms_between = $ss_between / $df_between;
    $ms_within = $ss_within / $df_within;

    $f_statistic = $ms_between / $ms_within;
    $critical_value = invf($df_between, $df_within);

    return [
        'groups' => $groups,
        'group_means' => $group_means,
        'overall_mean' => StatFunctions::zaokr($overall_mean, 4),
        'ss_between' => StatFunctions::zaokr($ss_between, 4),
        'ss_within' => StatFunctions::zaokr($ss_within, 4),
        'ss_total' => StatFunctions::zaokr($ss_between + $ss_within, 4),
        'df_between' => $df_between,
        'df_within' => $df_within,
        'df_total' => $df_total,
        'ms_between' => StatFunctions::zaokr($ms_between, 4),
        'ms_within' => StatFunctions::zaokr($ms_within, 4),
        'f_statistic' => StatFunctions::zaokr($f_statistic, 3),
        'critical_value' => $critical_value,
        'reject_null' => $f_statistic > $critical_value,
        'num_groups' => $num_groups,
        'group_sizes' => $group_sizes
    ];
}

?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jednoduché třídění (Analýza rozptylu ANOVA)</title>
    <link rel="stylesheet" href="assets/style.css">
    <script>
        function openFormula() {
            window.open('assets/images/anova.png', 'formula', 'width=850,height=500');
        }
        function closeFormula() {
            // Formula window will close automatically when this window closes
        }
    </script>
</head>
<body onunload="closeFormula()">
    <div class="container">
        <header>
            <div class="breadcrumb">
                <a href="celekTEST.php">← Zpět na seznam testů</a>
                <span class="separator">|</span>
                <a href="#" onclick="openFormula()">Zobrazit vzorce</a>
            </div>
            <h1>Jednoduché třídění (Analýza rozptylu ANOVA)</h1>
        </header>

        <main>
            <?php if ($error_message): ?>
                <div class="error-message">
                    <strong>Chyba:</strong> <?php echo Security::escape($error_message); ?>
                </div>
            <?php endif; ?>

            <?php switch ($a): case 0: ?>
                <div class="test-form">
                    <h2>Krok 1: Zadejte počet tříd</h2>
                    <p><strong>Hladina testu:</strong> α = 0,05</p>
                    <form method="get" class="input-form">
                        <div class="form-group">
                            <label for="in">Počet tříd (r):</label>
                            <input type="number"
                                   id="in"
                                   name="in"
                                   min="3"
                                   max="10"
                                   value="<?php echo Security::escape($in); ?>"
                                   required>
                            <span class="help-text">Zadejte celé číslo od 3 do 10</span>
                        </div>

                        <div class="form-actions">
                            <input type="hidden" name="a" value="1">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::getCsrfToken(); ?>">
                            <button type="submit" class="btn btn-primary">Pokračovat</button>
                        </div>
                    </form>
                </div>

            <?php break; case 1: ?>
                <div class="test-form">
                    <h2>Krok 2: Zadejte rozsahy tříd</h2>
                    <p><strong>Hladina testu:</strong> α = 0,05</p>

                    <form method="get" class="input-form">
                        <div class="form-group">
                            <label for="in">Počet tříd (r):</label>
                            <input type="number"
                                   id="in"
                                   name="in"
                                   min="3"
                                   max="10"
                                   value="<?php echo Security::escape($in); ?>"
                                   required>
                        </div>

                        <div class="form-group">
                            <label>Rozsah jednotlivých tříd n₁, ..., n<?php echo Security::escape($in); ?>:</label>
                            <div class="data-inputs">
                                <?php for ($i = 0; $i < $in; $i++): ?>
                                    <input type="number"
                                           min="2"
                                           max="10"
                                           name="n[]"
                                           value="<?php echo Security::escape($n[$i] ?? ''); ?>"
                                           placeholder="n<?php echo $i + 1; ?>"
                                           required>
                                <?php endfor; ?>
                            </div>
                            <span class="help-text">Zadejte čísla od 2 do 10</span>
                        </div>

                        <div class="form-actions">
                            <input type="hidden" name="a" value="2">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::getCsrfToken(); ?>">
                            <button type="submit" class="btn btn-primary">Pokračovat</button>
                        </div>
                    </form>
                </div>

            <?php break; case 2: ?>
                <div class="test-form">
                    <h2>Krok 3: Zadejte data</h2>
                    <p><strong>Hladina testu:</strong> α = 0,05</p>

                    <form method="get" class="input-form">
                        <div class="form-group">
                            <label for="in">Počet tříd (r):</label>
                            <input type="number"
                                   id="in"
                                   name="in"
                                   min="3"
                                   max="10"
                                   value="<?php echo Security::escape($in); ?>"
                                   required>
                        </div>

                        <div class="form-group">
                            <label>Rozsah jednotlivých tříd n₁, ..., n<?php echo Security::escape($in); ?>:</label>
                            <div class="data-inputs">
                                <?php for ($i = 0; $i < $in; $i++): ?>
                                    <input type="number"
                                           min="2"
                                           max="10"
                                           name="n[]"
                                           value="<?php echo Security::escape($n[$i] ?? ''); ?>"
                                           placeholder="n<?php echo $i + 1; ?>"
                                           required>
                                <?php endfor; ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Náhodné výběry z N(μ₁, σ²), ..., N(μ<?php echo Security::escape($in); ?>, σ²):</label>
                            <p class="hypothesis-text">
                                μ₁ = μ + α₁, ..., μ<?php echo Security::escape($in); ?> = μ + α<?php echo Security::escape($in); ?>, kde Σαₖ = 0
                            </p>

                            <?php
                            $data_index = 0;
                            for ($i = 0; $i < $in; $i++):
                                $group_size = $n[$i] ?? 0;
                            ?>
                                <div class="group-data">
                                    <label>Skupina <?php echo $i + 1; ?> (X<?php echo $i + 1; ?>,₁, ..., X<?php echo $i + 1; ?>,<?php echo Security::escape($group_size); ?>):</label>
                                    <div class="data-inputs">
                                        <?php for ($k = 0; $k < $group_size; $k++): ?>
                                            <input type="number"
                                                   step="any"
                                                   name="x[]"
                                                   value="<?php echo Security::escape($x[$data_index] ?? ''); ?>"
                                                   placeholder="X<?php echo $i + 1; ?>,<?php echo $k + 1; ?>"
                                                   required>
                                            <?php $data_index++; ?>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>

                        <div class="hypothesis">
                            <p><strong>Nulová hypotéza H₀:</strong> μ₁ = ... = μ<?php echo Security::escape($in); ?> (neboli α₁ = ... = α<?php echo Security::escape($in); ?> = 0)</p>
                        </div>

                        <div class="form-actions">
                            <input type="hidden" name="a" value="3">
                            <input type="hidden" name="csrf_token" value="<?php echo Security::getCsrfToken(); ?>">
                            <button type="submit" class="btn btn-primary">Proveďte test</button>
                        </div>
                    </form>
                </div>

            <?php break; case 3: ?>
                <?php if ($calculation_results): ?>
                    <div class="results">
                        <h2>Výsledky ANOVA testu</h2>

                        <div class="anova-table">
                            <h3>Tabulka analýzy rozptylu</h3>
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>Zdroj variability</th>
                                        <th>Součet čtverců</th>
                                        <th>Stupně volnosti</th>
                                        <th>Průměrný čtverec</th>
                                        <th>F-statistika</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Mezi skupinami</td>
                                        <td><?php echo Security::escape($calculation_results['ss_between']); ?></td>
                                        <td><?php echo Security::escape($calculation_results['df_between']); ?></td>
                                        <td><?php echo Security::escape($calculation_results['ms_between']); ?></td>
                                        <td rowspan="2"><?php echo Security::escape($calculation_results['f_statistic']); ?></td>
                                    </tr>
                                    <tr>
                                        <td>Uvnitř skupin</td>
                                        <td><?php echo Security::escape($calculation_results['ss_within']); ?></td>
                                        <td><?php echo Security::escape($calculation_results['df_within']); ?></td>
                                        <td><?php echo Security::escape($calculation_results['ms_within']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Celkem</strong></td>
                                        <td><strong><?php echo Security::escape($calculation_results['ss_total']); ?></strong></td>
                                        <td><strong><?php echo Security::escape($calculation_results['df_total']); ?></strong></td>
                                        <td>—</td>
                                        <td>—</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="results-summary">
                            <div class="result-item">
                                <strong>Celkový průměr:</strong> <?php echo Security::escape($calculation_results['overall_mean']); ?>
                            </div>
                            <div class="result-item">
                                <strong>F-statistika:</strong> <?php echo Security::escape($calculation_results['f_statistic']); ?>
                            </div>
                            <div class="result-item">
                                <strong>Kritická hodnota F₀,₀₅:</strong> <?php echo Security::escape($calculation_results['critical_value']); ?>
                            </div>
                        </div>

                        <div class="group-means">
                            <h3>Průměry skupin</h3>
                            <?php for ($i = 0; $i < $calculation_results['num_groups']; $i++): ?>
                                <div class="result-item">
                                    <strong>Skupina <?php echo $i + 1; ?> (n = <?php echo Security::escape($calculation_results['group_sizes'][$i]); ?>):</strong>
                                    X̄<?php echo $i + 1; ?> = <?php echo Security::escape(StatFunctions::zaokr($calculation_results['group_means'][$i], 4)); ?>
                                </div>
                            <?php endfor; ?>
                        </div>

                        <div class="conclusion <?php echo $calculation_results['reject_null'] ? 'reject' : 'accept'; ?>">
                            <?php if ($calculation_results['reject_null']): ?>
                                <h3>Závěr: Zamítáme H₀</h3>
                                <p>F > F₀,₀₅(<?php echo Security::escape($calculation_results['df_between']); ?>, <?php echo Security::escape($calculation_results['df_within']); ?>)</p>
                                <p>Průměry skupin se statisticky významně liší na hladině α = 0,05.</p>
                            <?php else: ?>
                                <h3>Závěr: Nezamítáme H₀</h3>
                                <p>F ≤ F₀,₀₅(<?php echo Security::escape($calculation_results['df_between']); ?>, <?php echo Security::escape($calculation_results['df_within']); ?>)</p>
                                <p>Průměry skupin se statisticky významně neliší na hladině α = 0,05.</p>
                            <?php endif; ?>
                        </div>

                        <div class="related-tests">
                            <h3>Související testy</h3>
                            <div class="test-links">
                                <a href="anovadvoj.php" class="btn btn-secondary">Dvojné třídění ANOVA</a>
                                <a href="kruskalwal.php" class="btn btn-secondary">Kruskal-Wallisův test</a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="form-actions">
                    <form method="get">
                        <input type="hidden" name="a" value="0">
                        <button type="submit" class="btn btn-outline">Nový vstup</button>
                    </form>
                </div>
            <?php endswitch; ?>
        </main>

        <footer>
            <div class="footer-content">
                <p>&copy; <?php echo Security::escape(APP_AUTHOR); ?></p>
            </div>
        </footer>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>