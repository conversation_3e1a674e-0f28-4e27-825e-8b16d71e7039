<?php
/**
 * <PERSON><PERSON><PERSON> to systematically process and fix all PHP files
 */

// Create new directory
$newDir = 'statistics_app_complete';
if (!is_dir($newDir)) {
    mkdir($newDir, 0755, true);
}

// List of all PHP files to process
$phpFiles = [
    'anova.php', 'anovadvoj.php', 'anovadvojinter.php', 'celekTEST.php', 'dvouv.php', 'dvouvF.php',
    'Eanova.php', 'Eanovadvoj.php', 'Eanovadvojinter.php', 'Eanovadvou.php', 'EcelekTEST.php',
    'Edvouv.php', 'EdvouvF.php', 'Efriedm.php', 'Ejednov.php', 'Ejednovjedn.php', 'EjednovS.php',
    'Ekontin.php', 'Ekruskalwal.php', 'Elinreg.php', 'Eparovy.php', 'Epears.php', 'E<PERSON>earman.php',
    'Ewildvouv.php', 'Ewiljednov.php', 'friedm.php', 'i.php', 'index.php', 'jednov.php',
    'jednovjedn.php', 'jednovjedn1.php', 'jednovS.php', 'kontin.php', 'kruskalwal.php',
    'linreg.php', 'parovy.php', 'pears.php', 'phpinfo.php', 'pokus.php', 'pokus1.php',
    'pokus3.php', 'pokus4.php', 'spearman.php', 'wildvouv.php', 'wiljednov.php'
];

function fixPhpFile($sourceFile, $targetFile) {
    if (!file_exists($sourceFile)) {
        echo "Source file not found: $sourceFile\n";
        return false;
    }
    
    $content = file_get_contents($sourceFile);
    
    // Apply all the fixes we know work
    
    // 1. Fix short PHP tags
    $content = preg_replace('/^<\?(?!\?php)/', '<?php', $content);
    $content = preg_replace('/(<\?(?!\?php))(?!\s*=)/', '<?php', $content);
    
    // 2. Add proper HTML5 doctype and charset
    if (!preg_match('/<!DOCTYPE/i', $content)) {
        if (preg_match('/^(\s*)<html>/i', $content)) {
            $content = preg_replace('/^(\s*)<html>/i', '$1<!DOCTYPE html>\n$1<html>', $content);
        }
    }
    
    // Add charset if missing
    if (preg_match('/<head>/i', $content) && !preg_match('/<meta[^>]+charset/i', $content)) {
        $content = preg_replace('/(<head>)/i', '$1\n    <meta charset="UTF-8">', $content);
    }
    
    // 3. Fix file handling functions
    $content = preg_replace('/FOpen\s*\(\s*"([^"]+)"\s*,\s*r\s*\)/', 'fopen("$1","r")', $content);
    $content = preg_replace('/fopen\s*\(\s*"([^"]+)"\s*,\s*r\s*\)/', 'fopen("$1","r")', $content);
    $content = preg_replace('/FOpen\s*\(/', 'fopen(', $content);
    $content = preg_replace('/FSeek\s*\(/', 'fseek(', $content);
    $content = preg_replace('/FRead\s*\(/', 'fread(', $content);
    $content = preg_replace('/FClose\s*\(/', 'fclose(', $content);
    
    // 4. Fix HTML attributes - add quotes
    $content = preg_replace('/(<[^>]+\s)(method|type|name|value|size|bgcolor|link|alink|vlink|onunload|onmouseover|width|height|align|colspan|rowspan)=([^"\s>]+)/', '$1$2="$3"', $content);
    
    // 5. Fix echo statements
    $content = preg_replace('/<\?echo\s*\(([^)]+)\)\s*;\s*\?>/', '<?php echo($1);?>', $content);
    $content = preg_replace('/<\?echo\s*\(([^)]+)\)\s*\?>/', '<?php echo($1);?>', $content);
    
    // 6. Fix input types
    $content = str_replace('type="integer"', 'type="number"', $content);
    $content = str_replace('type="double"', 'type="number" step="any"', $content);
    
    // 7. Fix variable initialization with null coalescing
    $patterns = [
        '/\$a = \$_GET\[\'a\'\];/' => '$a = $_GET[\'a\'] ?? 0;',
        '/\$n = \$_GET\[\'n\'\];/' => '$n = $_GET[\'n\'] ?? null;',
        '/\$x = \$_GET\[\'x\'\];/' => '$x = $_GET[\'x\'] ?? [];',
        '/\$y = \$_GET\[\'y\'\];/' => '$y = $_GET[\'y\'] ?? [];',
        '/\$aa = \$_GET\[\'aa\'\];/' => '$aa = $_GET[\'aa\'] ?? null;',
        '/\$m = \$_GET\[\'m\'\];/' => '$m = $_GET[\'m\'] ?? null;',
        '/\$c = \$_GET\[\'c\'\];/' => '$c = $_GET[\'c\'] ?? null;',
        '/\$in = \$_GET\[\'in\'\];/' => '$in = $_GET[\'in\'] ?? null;',
        '/\$ir = \$_GET\[\'ir\'\];/' => '$ir = $_GET[\'ir\'] ?? null;',
        '/\$is = \$_GET\[\'is\'\];/' => '$is = $_GET[\'is\'] ?? null;',
        '/\$ip = \$_GET\[\'ip\'\];/' => '$ip = $_GET[\'ip\'] ?? null;',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $content = preg_replace($pattern, $replacement, $content);
    }
    
    // 8. Fix array access with null coalescing in echo statements
    $content = preg_replace('/value="<\?php echo\(\$([a-zA-Z_][a-zA-Z0-9_]*)\);\?>"/', 'value="<?php echo($$$1 ?? \'\');?>"', $content);
    
    // 9. Add language attribute
    if (strpos($content, 'TESTOVÁNÍ') !== false || strpos($content, 'Jednovýběrový') !== false) {
        $content = preg_replace('/<html>/', '<html lang="cs">', $content);
    } elseif (strpos($content, 'HYPOTHESIS') !== false || strpos($content, 'One-sample') !== false) {
        $content = preg_replace('/<html>/', '<html lang="en">', $content);
    }
    
    // 10. Add title if missing
    if (preg_match('/<head>/', $content) && !preg_match('/<title>/', $content)) {
        if (strpos($content, 'TESTOVÁNÍ') !== false) {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Testování hypotéz</title>', $content);
        } elseif (strpos($content, 'HYPOTHESIS') !== false) {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Hypothesis Testing</title>', $content);
        } else {
            $content = preg_replace('/(<head>)/', '$1\n    <title>Statistické testy</title>', $content);
        }
    }
    
    // Write the fixed content
    file_put_contents($targetFile, $content);
    echo "Processed: $sourceFile -> $targetFile\n";
    return true;
}

// Process each file
$processedCount = 0;
foreach ($phpFiles as $file) {
    $sourceFile = "statisticsonweb.tf.czu.cz/$file";
    $targetFile = "$newDir/$file";
    
    if (fixPhpFile($sourceFile, $targetFile)) {
        $processedCount++;
    }
}

echo "\nProcessed $processedCount out of " . count($phpFiles) . " PHP files.\n";

// Copy all other files (images, PDFs, text files, etc.)
echo "\nCopying other files...\n";

$otherFiles = glob('statisticsonweb.tf.czu.cz/*');
$copiedCount = 0;

foreach ($otherFiles as $file) {
    if (is_file($file) && !preg_match('/\.php$/', $file)) {
        $filename = basename($file);
        $targetFile = "$newDir/$filename";
        
        if (copy($file, $targetFile)) {
            $copiedCount++;
            echo "Copied: $filename\n";
        }
    }
}

// Copy tex directory
if (is_dir('statisticsonweb.tf.czu.cz/tex')) {
    mkdir("$newDir/tex", 0755, true);
    $texFiles = glob('statisticsonweb.tf.czu.cz/tex/*');
    foreach ($texFiles as $texFile) {
        if (is_file($texFile)) {
            $filename = basename($texFile);
            copy($texFile, "$newDir/tex/$filename");
            echo "Copied tex file: $filename\n";
        }
    }
}

echo "\nCompleted! Copied $copiedCount other files.\n";
echo "All files are now in: $newDir/\n";
?>
